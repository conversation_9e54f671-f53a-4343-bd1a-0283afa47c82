项目类型：创建一个全新项目。
项目概述：使用 C++ 在 macOS 上实现一个极致高效的 IPv4 UDP 端口转发程序。程序通过命令行接受任意数量的参数，每个参数以空格分隔，格式为“接收源IP:接收源端口,接收目的IP:接收目的端口,转发源IP:转发源端口,转发目的IP:转发目的端口”。启动时解析并严格校验所有参数的语法与数值合法性。程序必须利用 macOS 原生的 kqueue、SO_REUSEPORT、零拷贝等技术，实现双向 UDP 转发：① 从“接收目的IP地址:接收目的端口号”socket收到来自“接收源IP地址:接收源端口号“的数据包，然后从“转发源IP地址:转发源端口号”socket转发到对应的“转发目的IP地址:转发目的端口号”；② 同时让从“转发源IP地址:转发源端口号”socket收到的来自“转发目的IP:转发目的端口号”的UDP数据包，再通过“接收目的IP地址:接收目的端口号”socket转发回于其对应的“接收源IP地址:接收源端口号“。要求：1）支持高并发、低延迟，使用非阻塞套接字和多线程/线程池或基于 io_uring（若可用）实现；2）提供详细的运行时日志，包括参数解析、绑定成功、转发统计、错误信息；3）实现优雅的信号处理（SIGINT、SIGTERM）以安全关闭所有 socket；4）代码必须符合 C++20 标准，使用 RAII 管理资源，避免内存泄漏；5）提供 CMake 构建脚本，支持 Release 与 Debug 两种模式，并在 Release 中开启最高级别的编译优化（-O3、-march=native）。请给出完整的项目结构说明、关键实现思路以及示例命令行调用方式。
项目测试：Please create a comprehensive test to validate the bidirectional UDP forwarding functionality using the mapping configuration `127.0.0.1:11110,127.0.0.1:11111,127.0.0.1:11112,127.0.0.1:11113`. Specifically:1. **Test Setup**: - Start the UDP forwarder with the mapping: `127.0.0.1:11110,127.0.0.1:11111,127.0.0.1:11112,127.0.0.1:11113`- This means: packets from 127.0.0.1:11110 received on 127.0.0.1:11111 will be forwarded to 127.0.0.1:11113 via 127.0.0.1:11112, and vice versa2. **Create Test Tools**:- Implement or use existing tools to create UDP clients/servers that can:- Send UDP packets from 127.0.0.1:11110 to 127.0.0.1:11111- Listen for packets on 127.0.0.1:11113 - Send response packets from 127.0.0.1:11113 to 127.0.0.1:11112- Verify packets are received back at 127.0.0.1:111103. **Functional Testing**:- Verify bidirectional packet forwarding works correctly- Test with various packet sizes (small, medium, large)- Confirm packet content integrity during forwarding- Validate that only packets from the correct source IPs are forwarded4. **Performance Metrics Collection**:- **Packet forwarding rate**: packets per second in both directions- **Data throughput rate**: bytes per second transferred- **System resource usage**: CPU utilization, memory consumption, network I/O- **Latency measurements**: round-trip time for forwarded packets- **Drop rate**: percentage of packets dropped vs successfully forwarded5. **Test Duration**: Run tests for at least 30 seconds to get stable metrics6. **Reporting**: Provide a summary of test results including all measured metrics and confirmation of bidirectional forwarding functionality.Use appropriate testing tools (such as netcat, iperf3, custom Python/C++ scripts, or system monitoring tools like top/htop) to generate traffic and measure performance.